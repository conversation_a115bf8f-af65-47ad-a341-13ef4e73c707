# Scoop Bucket 软件配置添加指南

## 1. 准备工作

### 收集软件信息
- 软件名称（用于文件名，建议小写，用连字符分隔）
- 最新版本号
- 官网地址
- 下载链接（支持不同架构）
- 许可证信息
- 软件描述

### 计算文件哈希
```powershell
# 下载文件后计算 SHA256 哈希
Get-FileHash -Algorithm SHA256 "文件路径"

# 或者使用在线工具
# 某些软件官方会提供哈希值文件
```

## 2. 创建 Manifest 文件

### 基本模板
```json
{
    "version": "版本号",
    "description": "软件描述",
    "homepage": "官网地址",
    "license": "许可证",
    "architecture": {
        "64bit": {
            "url": "下载链接",
            "hash": "sha256:哈希值"
        }
    },
    "bin": "可执行文件名"
}
```

### 常用字段说明

#### 必需字段
- `version`: 软件版本号
- `description`: 一行简短描述
- `homepage`: 软件官网
- `license`: 许可证（推荐使用 SPDX 标识符）

#### 下载相关
- `url`: 下载链接
- `hash`: 文件哈希值（格式：`sha256:哈希值`）
- `architecture`: 不同架构的配置

#### 安装相关
- `extract_dir`: 从压缩包中提取的目录
- `bin`: 添加到 PATH 的可执行文件
- `shortcuts`: 桌面快捷方式
- `installer`: 自定义安装脚本
- `persist`: 需要持久化的目录/文件

#### 自动更新
- `checkver`: 版本检查配置
- `autoupdate`: 自动更新配置

## 3. 实际示例

### 简单的便携软件
```json
{
    "version": "1.0.0",
    "description": "简单的便携软件",
    "homepage": "https://example.com",
    "license": "MIT",
    "url": "https://example.com/app.zip",
    "hash": "sha256:...",
    "bin": "app.exe"
}
```

### 复杂的多架构软件
```json
{
    "version": "2.0.0",
    "description": "支持多架构的复杂软件",
    "homepage": "https://example.com",
    "license": "Apache-2.0",
    "architecture": {
        "64bit": {
            "url": "https://example.com/app-x64.msi",
            "hash": "sha256:..."
        },
        "32bit": {
            "url": "https://example.com/app-x86.msi", 
            "hash": "sha256:..."
        }
    },
    "installer": {
        "script": "Start-Process msiexec -ArgumentList '/i', '$fname', '/quiet' -Wait"
    },
    "shortcuts": [
        ["app.exe", "My App"]
    ]
}
```

## 4. 测试和验证

### 本地测试
```powershell
# 安装测试
scoop install bucket\your-app.json

# 验证安装
your-app --version

# 卸载测试
scoop uninstall your-app
```

### 验证清单
- [ ] JSON 格式正确
- [ ] 所有必需字段已填写
- [ ] 哈希值正确
- [ ] 下载链接有效
- [ ] 软件能正常安装和运行
- [ ] 卸载干净

## 5. 提交到仓库

```bash
git add bucket/your-app.json
git commit -m "Add your-app v1.0.0"
git push
```

## 6. 常见问题

### Q: 如何处理需要安装程序的软件？
A: 使用 `installer` 字段定义安装脚本，或者使用 URL 片段技巧绕过安装程序。

### Q: 如何添加自动更新？
A: 配置 `checkver` 和 `autoupdate` 字段，支持 GitHub releases、网页正则匹配等。

### Q: 如何处理配置文件持久化？
A: 使用 `persist` 字段指定需要保留的目录或文件。

## 7. 参考资源

- [官方 App Manifests 文档](https://github.com/ScoopInstaller/Scoop/wiki/App-Manifests)
- [主仓库示例](https://github.com/ScoopInstaller/Main/tree/master/bucket)
- [Extras 仓库示例](https://github.com/ScoopInstaller/Extras/tree/master/bucket)
